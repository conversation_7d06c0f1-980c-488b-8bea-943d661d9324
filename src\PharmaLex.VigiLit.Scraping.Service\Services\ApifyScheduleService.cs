using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyScheduleService : IApifyScheduleService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyScheduleService> _logger;

    public ApifyScheduleService(IApifyClient apifyClient, ILogger<ApifyScheduleService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task CreateScheduleForTaskAsync(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken = default)
    {
        try
        {
            await _apifyClient.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", cancellationToken);

            _logger.LogInformation("Created Apify schedule '{ScheduleName}' for task '{TaskId}'",
                scheduleName, taskId);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify schedule '{scheduleName}' for task '{taskId}' with cron expression '{cronExpression}'";
            _logger.LogError(ex, "Failed to create Apify schedule '{ScheduleName}' for task '{TaskId}' with cron expression '{CronExpression}': {ExceptionMessage}", scheduleName, taskId, cronExpression, ex.Message);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }
}
