using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyWebhookService : IApifyWebhookService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyWebhookService> _logger;

    public ApifyWebhookService(IApifyClient apifyClient, ILogger<ApifyWebhookService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task CreateWebhookForTaskAsync(string taskId, string webhookUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            await _apifyClient.CreateWebhookAsync(webhookUrl, taskId, cancellationToken);

            _logger.LogInformation("Created Apify webhook for task '{TaskId}' with URL '{WebhookUrl}'", taskId, webhookUrl);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify webhook for task '{taskId}' with URL '{webhookUrl}'";
            _logger.LogError(ex, "Failed to create Apify webhook for task '{TaskId}' with URL '{WebhookUrl}': {ExceptionMessage}", taskId, webhookUrl, ex.Message);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }
}
